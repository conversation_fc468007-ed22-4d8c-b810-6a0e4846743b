import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ScheduleModule } from "@nestjs/schedule";
import { ThrottlerModule } from "@nestjs/throttler";
import { TerminusModule } from "@nestjs/terminus";
import { APP_GUARD, APP_FILTER } from "@nestjs/core";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { AuthModule } from "./auth/auth.module";
import { UsersModule } from "./users/users.module";
import { DomainsModule } from "./domains/domains.module";
import { TargetDomainsModule } from "./target-domains/target-domains.module";
import { AnalysisModule } from "./analysis/analysis.module";
import { EmailsModule } from "./emails/emails.module";
import { DashboardModule } from "./dashboard/dashboard.module";
import { CacheModule } from "./cache/cache.module";
import { QueueModule } from "./queue/queue.module";
import { ThirdPartyModule } from "./third-party/third-party.module";
import { JwtAuthGuard } from "./auth/guards/jwt-auth.guard";
import { GlobalExceptionFilter } from "./common/filters/global-exception.filter";
import { RetryService } from "./common/services/retry.service";
import { HealthModule } from "./health/health.module";

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env.development", ".env.local", ".env"],
    }),

    // Database (optional - only if DB_ENABLED is true)
    ...(process.env.DB_ENABLED === "true"
      ? [
          TypeOrmModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => ({
              type: "postgres",
              host: configService.get("DB_HOST", "localhost"),
              port: configService.get("DB_PORT", 5432),
              username: configService.get("DB_USERNAME", "postgres"),
              password: configService.get("DB_PASSWORD", ""),
              database: configService.get("DB_DATABASE", "backlink"),
              entities: [__dirname + "/**/*.entity{.ts,.js}"],
              migrations: [__dirname + "/database/migrations/*{.ts,.js}"],
              synchronize: false, // Always use migrations in production
              migrationsRun: configService.get("NODE_ENV") === "production",
              logging: configService.get("NODE_ENV") === "development",
              ssl:
                configService.get("DB_SSL", "false") === "true"
                  ? {
                      rejectUnauthorized: false,
                    }
                  : undefined,
            }),
          }),
        ]
      : []),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get("THROTTLE_TTL", 60),
        limit: configService.get("THROTTLE_LIMIT", 100),
      }),
    }),

    // Scheduling
    ScheduleModule.forRoot(),

    // Health checks
    TerminusModule,

    // Feature modules
    AuthModule,
    UsersModule,
    DomainsModule,
    TargetDomainsModule,
    AnalysisModule,
    EmailsModule,
    DashboardModule,

    // Infrastructure modules
    CacheModule,
    QueueModule,
    ThirdPartyModule,
    HealthModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    RetryService,
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    // Uncomment when authentication is needed
    // {
    //   provide: APP_GUARD,
    //   useClass: JwtAuthGuard,
    // },
  ],
})
export class AppModule {}
