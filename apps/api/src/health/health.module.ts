import { Module } from "@nestjs/common";
import { TerminusModule } from "@nestjs/terminus";
import { HealthController } from "./health.controller";
import { HealthService } from "./health.service";
import { QueueModule } from "../queue/queue.module";
import { CacheModule } from "../cache/cache.module";

@Module({
  imports: [TerminusModule, QueueModule, CacheModule],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}
