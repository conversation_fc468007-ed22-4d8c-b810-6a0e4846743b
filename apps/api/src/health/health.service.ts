import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { QueueService } from "../queue/queue.service";
import { CacheService } from "../cache/cache.service";

import * as os from "os";
import * as process from "process";

export interface HealthStatus {
  status: "healthy" | "unhealthy" | "degraded";
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    queues: ServiceHealth;
    extractors: ServiceHealth;
  };
  system: {
    memory: SystemMetrics;
    cpu: SystemMetrics;
    disk: SystemMetrics;
  };
  dependencies: {
    ahrefs: ServiceHealth;
    similarweb: ServiceHealth;
    whoisxml: ServiceHealth;
  };
}

export interface ServiceHealth {
  status: "healthy" | "unhealthy" | "degraded";
  responseTime?: number;
  lastCheck: string;
  error?: string;
  details?: any;
}

export interface SystemMetrics {
  usage: number;
  total: number;
  free: number;
  percentage: number;
}

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly startTime = Date.now();
  private healthCache: { data: HealthStatus; timestamp: number } | null = null;
  private readonly cacheTtl = 30000; // 30 seconds

  constructor(
    private readonly configService: ConfigService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly queueService: QueueService,
    private readonly cacheService: CacheService
  ) {}

  async getDetailedHealthStatus(): Promise<HealthStatus> {
    // Return cached result if still valid
    if (
      this.healthCache &&
      Date.now() - this.healthCache.timestamp < this.cacheTtl
    ) {
      return this.healthCache.data;
    }

    const timestamp = new Date().toISOString();
    const uptime = Date.now() - this.startTime;

    try {
      const [
        databaseHealth,
        redisHealth,
        queuesHealth,
        extractorsHealth,
        dependenciesHealth,
        systemMetrics,
      ] = await Promise.allSettled([
        this.checkDatabaseHealth(),
        this.checkRedisHealth(),
        this.checkQueuesHealth(),
        this.mockExtractorsHealth(),
        this.checkDependenciesHealth(),
        this.getSystemMetrics(),
      ]);

      const healthStatus: HealthStatus = {
        status: this.calculateOverallStatus([
          this.getSettledValue(databaseHealth)?.status,
          this.getSettledValue(redisHealth)?.status,
          this.getSettledValue(queuesHealth)?.status,
          this.getSettledValue(extractorsHealth)?.status,
        ]),
        timestamp,
        uptime,
        version: process.env.npm_package_version || "unknown",
        environment: this.configService.get("NODE_ENV", "development"),
        services: {
          database:
            this.getSettledValue(databaseHealth) ||
            this.createUnhealthyService("Database check failed"),
          redis:
            this.getSettledValue(redisHealth) ||
            this.createUnhealthyService("Redis check failed"),
          queues:
            this.getSettledValue(queuesHealth) ||
            this.createUnhealthyService("Queues check failed"),
          extractors:
            this.getSettledValue(extractorsHealth) ||
            this.createUnhealthyService("Extractors check failed"),
        },
        system:
          this.getSettledValue(systemMetrics) || this.getDefaultSystemMetrics(),
        dependencies:
          this.getSettledValue(dependenciesHealth) ||
          this.getDefaultDependencies(),
      };

      // Cache the result
      this.healthCache = {
        data: healthStatus,
        timestamp: Date.now(),
      };

      return healthStatus;
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  async checkReadiness(): Promise<{
    status: string;
    ready: boolean;
    checks: any;
  }> {
    try {
      const checks = await Promise.allSettled([
        this.checkDatabaseHealth(),
        this.checkRedisHealth(),
      ]);

      const ready = checks.every(
        (check) =>
          check.status === "fulfilled" && check.value.status === "healthy"
      );

      return {
        status: ready ? "ready" : "not ready",
        ready,
        checks: {
          database: this.getSettledValue(checks[0]),
          redis: this.getSettledValue(checks[1]),
        },
      };
    } catch (error) {
      return {
        status: "not ready",
        ready: false,
        checks: { error: error.message },
      };
    }
  }

  async checkLiveness(): Promise<{
    status: string;
    alive: boolean;
    uptime: number;
  }> {
    const uptime = Date.now() - this.startTime;

    // Simple liveness check - if we can respond, we're alive
    return {
      status: "alive",
      alive: true,
      uptime,
    };
  }

  async getApplicationMetrics(): Promise<any> {
    try {
      const [queueStats, systemMetrics] = await Promise.allSettled([
        this.getQueueMetrics(),
        this.getSystemMetrics(),
      ]);

      return {
        requests: {
          total: 0, // Would be tracked by middleware
          successful: 0,
          failed: 0,
          averageResponseTime: 0,
        },
        performance: {
          uptime: Date.now() - this.startTime,
          memory: this.getSettledValue(systemMetrics)?.memory,
          cpu: this.getSettledValue(systemMetrics)?.cpu,
        },
        errors: {
          total: 0, // Would be tracked by error handler
          rate: 0,
          lastError: null,
        },
        queues: this.getSettledValue(queueStats) || {},
        cache: {
          hits: 0, // Would be tracked by cache service
          misses: 0,
          hitRate: 0,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get application metrics: ${error.message}`);
      throw error;
    }
  }

  private async checkDatabaseHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      await this.dataSource.query("SELECT 1");

      return {
        status: "healthy",
        responseTime: Date.now() - startTime,
        lastCheck: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: "unhealthy",
        responseTime: Date.now() - startTime,
        lastCheck: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  private async checkRedisHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      await this.cacheService.set("health_check", "ok", 10);
      const result = await this.cacheService.get("health_check");

      if (result !== "ok") {
        throw new Error("Redis health check failed");
      }

      return {
        status: "healthy",
        responseTime: Date.now() - startTime,
        lastCheck: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: "unhealthy",
        responseTime: Date.now() - startTime,
        lastCheck: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  private async checkQueuesHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      const queueNames = this.queueService.getQueueNames();
      const stats = await Promise.all(
        queueNames.map((name) => this.queueService.getQueueStats(name))
      );

      const hasUnhealthyQueue = stats.some((stat) => stat === null);

      return {
        status: hasUnhealthyQueue ? "degraded" : "healthy",
        responseTime: Date.now() - startTime,
        lastCheck: new Date().toISOString(),
        details: { queueCount: queueNames.length, stats },
      };
    } catch (error) {
      return {
        status: "unhealthy",
        responseTime: Date.now() - startTime,
        lastCheck: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  private async mockExtractorsHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();

    // Mock extractor health check
    const mockExtractors = {
      ahrefs: { healthy: !!this.configService.get("AHREFS_API_KEY") },
      similarweb: { healthy: !!this.configService.get("SIMILARWEB_API_KEY") },
      whoisxml: { healthy: !!this.configService.get("WHOISXML_API_KEY") },
      webscraper: { healthy: true }, // Always available
    };

    const extractors = Object.values(mockExtractors);
    const healthyCount = extractors.filter((e) => e.healthy).length;
    const totalCount = extractors.length;

    let status: "healthy" | "unhealthy" | "degraded" = "healthy";
    if (healthyCount === 0) {
      status = "unhealthy";
    } else if (healthyCount < totalCount) {
      status = "degraded";
    }

    return {
      status,
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString(),
      details: {
        healthy: healthyCount,
        total: totalCount,
        extractors: mockExtractors,
      },
    };
  }

  private async checkDependenciesHealth(): Promise<{
    ahrefs: ServiceHealth;
    similarweb: ServiceHealth;
    whoisxml: ServiceHealth;
  }> {
    // This would check external API health
    // For now, return based on configuration
    const timestamp = new Date().toISOString();

    return {
      ahrefs: {
        status: this.configService.get("AHREFS_API_KEY")
          ? "healthy"
          : "degraded",
        lastCheck: timestamp,
        details: { configured: !!this.configService.get("AHREFS_API_KEY") },
      },
      similarweb: {
        status: this.configService.get("SIMILARWEB_API_KEY")
          ? "healthy"
          : "degraded",
        lastCheck: timestamp,
        details: { configured: !!this.configService.get("SIMILARWEB_API_KEY") },
      },
      whoisxml: {
        status: this.configService.get("WHOISXML_API_KEY")
          ? "healthy"
          : "degraded",
        lastCheck: timestamp,
        details: { configured: !!this.configService.get("WHOISXML_API_KEY") },
      },
    };
  }

  private async getSystemMetrics(): Promise<{
    memory: SystemMetrics;
    cpu: SystemMetrics;
    disk: SystemMetrics;
  }> {
    const memoryUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    return {
      memory: {
        usage: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        free: memoryUsage.heapTotal - memoryUsage.heapUsed,
        percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
      },
      cpu: {
        usage: 0, // Would need more complex calculation
        total: os.cpus().length,
        free: 0,
        percentage: 0,
      },
      disk: {
        usage: usedMemory,
        total: totalMemory,
        free: freeMemory,
        percentage: (usedMemory / totalMemory) * 100,
      },
    };
  }

  private async getQueueMetrics(): Promise<any> {
    try {
      const queueNames = this.queueService.getQueueNames();
      const metrics: any = {};

      for (const queueName of queueNames) {
        const stats = await this.queueService.getQueueStats(queueName);
        metrics[queueName] = stats;
      }

      return metrics;
    } catch (error) {
      this.logger.error(`Failed to get queue metrics: ${error.message}`);
      return {};
    }
  }

  private calculateOverallStatus(
    statuses: (string | undefined)[]
  ): "healthy" | "unhealthy" | "degraded" {
    const validStatuses = statuses.filter(Boolean);

    if (validStatuses.includes("unhealthy")) {
      return "unhealthy";
    }

    if (validStatuses.includes("degraded")) {
      return "degraded";
    }

    return "healthy";
  }

  private getSettledValue<T>(result: PromiseSettledResult<T>): T | null {
    return result.status === "fulfilled" ? result.value : null;
  }

  private createUnhealthyService(error: string): ServiceHealth {
    return {
      status: "unhealthy",
      lastCheck: new Date().toISOString(),
      error,
    };
  }

  private getDefaultSystemMetrics(): any {
    return {
      memory: { usage: 0, total: 0, free: 0, percentage: 0 },
      cpu: { usage: 0, total: 0, free: 0, percentage: 0 },
      disk: { usage: 0, total: 0, free: 0, percentage: 0 },
    };
  }

  private getDefaultDependencies(): any {
    const timestamp = new Date().toISOString();
    return {
      ahrefs: { status: "degraded", lastCheck: timestamp },
      similarweb: { status: "degraded", lastCheck: timestamp },
      whoisxml: { status: "degraded", lastCheck: timestamp },
    };
  }
}
