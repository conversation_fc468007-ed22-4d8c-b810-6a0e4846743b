import { <PERSON>du<PERSON>, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AnalysisService } from "./analysis.service";
import { AnalysisController } from "./analysis.controller";
import { DomainAnalysis } from "./entities/domain-analysis.entity";
import { DomainsModule } from "../domains/domains.module";
import { TargetDomainsModule } from "../target-domains/target-domains.module";
import { HttpModule } from "@nestjs/axios";
import { ConfigModule } from "@nestjs/config";
import { WebScraperService } from "./services/web-scraper.service";
import { ContactExtractorService } from "./services/contact-extractor.service";
import { ContentAnalyzerService } from "./services/content-analyzer.service";
import { SeoApiService } from "./services/seo-api.service";
import { DomainAnalyzerService } from "./services/domain-analyzer.service";
import { BacklinkScoringService } from "./services/backlink-scoring.service";
import { DomainInfoExtractorRegistryService } from "./services/domain-info-extractor.registry";
import { AhrefsDomainExtractor } from "./extractors/ahrefs-domain-extractor";
import { SimilarWebDomainExtractor } from "./extractors/similarweb-domain-extractor";
import { WhoisXmlDomainExtractor } from "./extractors/whoisxml-domain-extractor";
import { WebScraperDomainExtractor } from "./extractors/web-scraper-domain-extractor";
import { BatchAnalysisController } from "./controllers/batch-analysis.controller";
import { ThirdPartyModule } from "../third-party/third-party.module";
import { CacheModule } from "../cache/cache.module";
import { QueueModule } from "../queue/queue.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([DomainAnalysis]),
    HttpModule,
    ConfigModule,
    DomainsModule,
    forwardRef(() => TargetDomainsModule),
    ThirdPartyModule,
    CacheModule,
    forwardRef(() => QueueModule),
  ],
  providers: [
    AnalysisService,
    WebScraperService,
    ContactExtractorService,
    ContentAnalyzerService,
    SeoApiService,
    DomainAnalyzerService,
    BacklinkScoringService,
    DomainInfoExtractorRegistryService,
    AhrefsDomainExtractor,
    SimilarWebDomainExtractor,
    WhoisXmlDomainExtractor,
    WebScraperDomainExtractor,
  ],
  controllers: [AnalysisController, BatchAnalysisController],
  exports: [
    AnalysisService,
    BacklinkScoringService,
    DomainAnalyzerService,
    ContactExtractorService,
    DomainInfoExtractorRegistryService,
  ],
})
export class AnalysisModule {
  constructor(
    private readonly extractorRegistry: DomainInfoExtractorRegistryService,
    private readonly ahrefsExtractor: AhrefsDomainExtractor,
    private readonly similarWebExtractor: SimilarWebDomainExtractor,
    private readonly whoisXmlExtractor: WhoisXmlDomainExtractor,
    private readonly webScraperExtractor: WebScraperDomainExtractor
  ) {
    // Register all extractors on module initialization
    this.extractorRegistry.register(this.ahrefsExtractor);
    this.extractorRegistry.register(this.similarWebExtractor);
    this.extractorRegistry.register(this.whoisXmlExtractor);
    this.extractorRegistry.register(this.webScraperExtractor);
  }
}
