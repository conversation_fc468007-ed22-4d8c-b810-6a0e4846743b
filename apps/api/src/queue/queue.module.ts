import { Module, forwardRef } from "@nestjs/common";
import { BullModule } from "@nestjs/bull";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { QueueService } from "./queue.service";
import { DomainAnalysisProcessor } from "./processors/domain-analysis.processor";
import { BatchAnalysisProcessor } from "./processors/batch-analysis.processor";
import { ApiRateLimitProcessor } from "./processors/api-rate-limit.processor";
import { QueueController } from "./queue.controller";
import { ThirdPartyModule } from "../third-party/third-party.module";
import { CacheModule } from "../cache/cache.module";

export const DOMAIN_ANALYSIS_QUEUE = "domain-analysis";
export const BATCH_ANALYSIS_QUEUE = "batch-analysis";
export const API_RATE_LIMIT_QUEUE = "api-rate-limit";

@Module({
  imports: [
    ConfigModule,
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get("REDIS_HOST", "localhost"),
          port: configService.get("REDIS_PORT", 6379),
          password: configService.get("REDIS_PASSWORD"),
          db: configService.get("REDIS_DB", 0),
          retryDelayOnFailover: 100,
          enableReadyCheck: false,
          maxRetriesPerRequest: null,
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 2000,
          },
        },
      }),
    }),
    BullModule.registerQueue(
      { name: DOMAIN_ANALYSIS_QUEUE },
      { name: BATCH_ANALYSIS_QUEUE },
      { name: API_RATE_LIMIT_QUEUE }
    ),
    ThirdPartyModule,
    CacheModule,
  ],
  controllers: [QueueController],
  providers: [
    QueueService,
    DomainAnalysisProcessor,
    BatchAnalysisProcessor,
    ApiRateLimitProcessor,
  ],
  exports: [QueueService, BullModule],
})
export class QueueModule {}
